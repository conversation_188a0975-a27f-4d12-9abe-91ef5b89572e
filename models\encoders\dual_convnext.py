# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import functools
from collections import OrderedDict
import time
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from timm.models.layers import trunc_normal_, DropPath

from utils.load_utils import load_state_dict
from engine.logger import get_logger

from modules.FFM_change import FeatureFusionModule as FFM
from modules.FRM_change import FeatureCorrection_s2c

logger = get_logger()


class LayerNorm(nn.Module):
    """ LayerNorm that supports two data formats: channels_last (default) or channels_first. 
    The ordering of the dimensions in the inputs. channels_last corresponds to inputs with 
    shape (batch_size, height, width, channels) while channels_first corresponds to inputs 
    with shape (batch_size, channels, height, width).
    """
    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError 
        self.normalized_shape = (normalized_shape, )
    
    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x


class GRN(nn.Module):
    """ GRN (Global Response Normalization) layer
    """
    def __init__(self, dim):
        super().__init__()
        self.gamma = nn.Parameter(torch.zeros(1, 1, 1, dim))
        self.beta = nn.Parameter(torch.zeros(1, 1, 1, dim))

    def forward(self, x):
        Gx = torch.norm(x, p=2, dim=(1,2), keepdim=True)
        Nx = Gx / (Gx.mean(dim=-1, keepdim=True) + 1e-6)
        return self.gamma * (x * Nx) + self.beta + x


class Block(nn.Module):
    """ ConvNeXtV2 Block.
    
    Args:
        dim (int): Number of input channels.
        drop_path (float): Stochastic depth rate. Default: 0.0
    """
    def __init__(self, dim, drop_path=0.):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim) # depthwise conv
        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim) # pointwise/1x1 convs, implemented with linear layers
        self.act = nn.GELU()
        self.grn = GRN(4 * dim)
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1) # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.grn(x)
        x = self.pwconv2(x)
        x = x.permute(0, 3, 1, 2) # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x


class DualConvNeXtV2(nn.Module):
    """ Dual ConvNeXt V2 backbone for RGB-SAR fusion.

    Args:
        in_chans (int): Number of input image channels. Default: 3
        depths (tuple(int)): Number of blocks at each stage. Default: [3, 3, 27, 3]
        dims (int): Feature dimension at each stage. Default: [128, 256, 512, 1024]
        drop_path_rate (float): Stochastic depth rate. Default: 0.
        out_indices (Sequence[int]): Output from which stages.
        frozen_stages (int): Stages to be frozen (stop grad and set eval mode).
            -1 means not freezing any parameters.
        norm_fuse (nn.Module): Normalization layer for fusion modules.
    """
    def __init__(self,
                 in_chans=3,
                 depths=[3, 3, 27, 3],
                 dims=[128, 256, 512, 1024],
                 drop_path_rate=0.,
                 out_indices=(0, 1, 2, 3),
                 frozen_stages=-1,
                 norm_fuse=nn.BatchNorm2d):
        super().__init__()

        self.depths = depths
        self.out_indices = out_indices
        self.frozen_stages = frozen_stages
        self.num_stages = len(depths)

        # RGB branch downsample layers
        self.downsample_layers = nn.ModuleList()
        # stem layer for RGB (3 channels)
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)

        # SAR branch downsample layers
        self.downsample_layers_d = nn.ModuleList()
        # stem layer for SAR (1 channel)
        stem_d = nn.Sequential(
            nn.Conv2d(1, dims[0], kernel_size=4, stride=4),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers_d.append(stem_d)

        # intermediate downsampling layers
        for i in range(3):
            downsample_layer = nn.Sequential(
                LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

            downsample_layer_d = nn.Sequential(
                LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers_d.append(downsample_layer_d)

        # RGB branch stages
        self.stages = nn.ModuleList()
        # SAR branch stages
        self.stages_d = nn.ModuleList()

        # Feature Rectification Modules
        self.FRMs = nn.ModuleList()
        # Feature Fusion Modules
        self.FFMs = nn.ModuleList()

        dp_rates = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0

        for i in range(4):
            # RGB stage
            stage = nn.Sequential(
                *[Block(dim=dims[i], drop_path=dp_rates[cur + j]) for j in range(depths[i])]
            )
            self.stages.append(stage)

            # SAR stage
            stage_d = nn.Sequential(
                *[Block(dim=dims[i], drop_path=dp_rates[cur + j]) for j in range(depths[i])]
            )
            self.stages_d.append(stage_d)

            # Feature Rectification Module
            frm = FeatureCorrection_s2c(dim=dims[i])
            self.FRMs.append(frm)

            # Feature Fusion Module
            # For ConvNeXt, we use 8 heads as default (similar to num_heads in Swin)
            num_heads = max(dims[i] // 32, 4)  # Ensure at least 4 heads
            fuse = FFM(dim=dims[i], reduction=1, num_heads=num_heads, norm_layer=norm_fuse)
            self.FFMs.append(fuse)

            cur += depths[i]

        self.num_features = dims

        # add a norm layer for each output
        for i_layer in out_indices:
            layer = LayerNorm(dims[i_layer], eps=1e-6, data_format="channels_first")
            layer_name = f'norm{i_layer}'
            self.add_module(layer_name, layer)
            layer_d = LayerNorm(dims[i_layer], eps=1e-6, data_format="channels_first")
            layer_name_d = f'norm_d{i_layer}'
            self.add_module(layer_name_d, layer_d)

        self.apply(self._init_weights)
        self._freeze_stages()

    def _init_weights(self, m):
        if isinstance(m, (nn.Conv2d, nn.Linear)):
            trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def _freeze_stages(self):
        if self.frozen_stages >= 0:
            # Freeze RGB stem
            for param in self.downsample_layers[0].parameters():
                param.requires_grad = False
            # Freeze SAR stem
            for param in self.downsample_layers_d[0].parameters():
                param.requires_grad = False

        if self.frozen_stages >= 1:
            for i in range(0, min(self.frozen_stages, self.num_stages)):
                # Freeze RGB stages
                for param in self.stages[i].parameters():
                    param.requires_grad = False
                # Freeze SAR stages
                for param in self.stages_d[i].parameters():
                    param.requires_grad = False
                # Freeze downsampling layers
                if i < self.num_stages - 1:
                    for param in self.downsample_layers[i+1].parameters():
                        param.requires_grad = False
                    for param in self.downsample_layers_d[i+1].parameters():
                        param.requires_grad = False

    def init_weights(self, pretrained=None):
        """Initialize the weights in backbone.
        Args:
            pretrained (str, optional): Path to pre-trained weights.
                Defaults to None.
        """
        def _init_weights(m):
            if isinstance(m, (nn.Conv2d, nn.Linear)):
                trunc_normal_(m.weight, std=.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)

        if isinstance(pretrained, str):
            self.apply(_init_weights)
            load_dualpath_convnext_model(self, pretrained)
        elif pretrained is None:
            self.apply(_init_weights)
        else:
            raise TypeError('pretrained must be a str or None')

    def forward(self, x, x_d):
        """Forward function."""
        outs = []

        # Process through each stage
        for i in range(self.num_stages):
            # Downsample
            x = self.downsample_layers[i](x)
            x_d = self.downsample_layers_d[i](x_d)

            # Stage processing
            x = self.stages[i](x)
            x_d = self.stages_d[i](x_d)

            # Feature Rectification with FeatureCorrection_s2c
            x, x_d = self.FRMs[i](x, x_d)

            # Store outputs for fusion
            x_out, x_out_d = x, x_d

            if i in self.out_indices:
                # Apply normalization
                norm_layer = getattr(self, f'norm{i}')
                x_out = norm_layer(x_out)

                norm_layer_d = getattr(self, f'norm_d{i}')
                x_out_d = norm_layer_d(x_out_d)

                # Feature fusion
                out = self.FFMs[i](x_out, x_out_d)
                outs.append(out)

        return tuple(outs)

    def train(self, mode=True):
        """Convert the model into training mode while keep layers freezed."""
        super(DualConvNeXtV2, self).train(mode)
        self._freeze_stages()


class convnext_base(DualConvNeXtV2):
    def __init__(self, **kwargs):
        super(convnext_base, self).__init__(
            in_chans=3,
            depths=[3, 3, 27, 3],
            dims=[128, 256, 512, 1024],
            drop_path_rate=0.1,
            out_indices=(0, 1, 2, 3),
            frozen_stages=-1,
            norm_fuse=nn.BatchNorm2d,
            **kwargs
        )


class convnext_tiny(DualConvNeXtV2):
    def __init__(self, **kwargs):
        super(convnext_tiny, self).__init__(
            in_chans=3,
            depths=[3, 3, 9, 3],
            dims=[96, 192, 384, 768],
            drop_path_rate=0.1,
            out_indices=(0, 1, 2, 3),
            frozen_stages=-1,
            norm_fuse=nn.BatchNorm2d,
            **kwargs
        )


def load_dualpath_convnext_model(model, model_file, is_restore=False):
    """Load pretrained ConvNeXt weights into dual-path model.

    Args:
        model: Dual-path ConvNeXt model
        model_file: Path to pretrained weights file
        is_restore: Whether this is a restore operation
    """
    # load raw state_dict
    t_start = time.time()
    if isinstance(model_file, str):
        raw_state_dict = torch.load(model_file, map_location=torch.device('cpu'))
        if 'model' in raw_state_dict.keys():
            raw_state_dict = raw_state_dict['model']
    else:
        raw_state_dict = model_file

    # copy to dual backbone
    state_dict = {}
    for k, v in raw_state_dict.items():
        if k.find('downsample_layers') >= 0:
            # Handle downsample layers
            state_dict[k] = v
            # For SAR branch downsample layers
            if 'downsample_layers.0.0.weight' in k:
                # RGB stem has shape [dims[0], 3, 4, 4]
                # SAR stem needs shape [dims[0], 1, 4, 4]
                # We take the mean across the channel dimension
                if v.shape[1] == 3:  # RGB channels
                    v_d = v.mean(dim=1, keepdim=True)  # Average across RGB channels
                    state_dict[k.replace('downsample_layers', 'downsample_layers_d')] = v_d
                else:
                    state_dict[k.replace('downsample_layers', 'downsample_layers_d')] = v
            else:
                # For other downsample parameters, copy directly
                state_dict[k.replace('downsample_layers', 'downsample_layers_d')] = v
        elif k.find('stages') >= 0:
            # Handle stage layers
            state_dict[k] = v
            state_dict[k.replace('stages', 'stages_d')] = v
        elif k.find('norm') >= 0 and not k.find('norm_d') >= 0:
            # Handle norm layers (avoid duplicating norm_d)
            state_dict[k] = v
            state_dict[k.replace('norm', 'norm_d')] = v
        elif k.find('head') >= 0:
            # Skip head layers as they are not needed for backbone
            continue
        else:
            # Copy other parameters directly
            state_dict[k] = v

    t_ioend = time.time()

    if is_restore:
        new_state_dict = OrderedDict()
        for k, v in state_dict.items():
            name = 'module.' + k
            new_state_dict[name] = v
        state_dict = new_state_dict

    # Load state dict with strict=False to allow missing keys
    missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)

    # Count loaded parameters
    total_params = len([name for name, _ in model.named_parameters()])
    loaded_params = total_params - len(missing_keys)

    del state_dict
    t_end = time.time()

    logger.info(
        f"Load ConvNeXt model, Time usage:\n\tIO: {t_ioend - t_start:.3f}s, "
        f"initialize parameters: {t_end - t_ioend:.3f}s"
    )
    logger.info(
        f"Loaded {loaded_params}/{total_params} parameters "
        f"({100.0 * loaded_params / total_params:.1f}%)"
    )

    if missing_keys:
        logger.info(f"Missing keys: {len(missing_keys)} (fusion modules expected)")
    if unexpected_keys:
        logger.info(f"Unexpected keys: {len(unexpected_keys)}")

    return model
