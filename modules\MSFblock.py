import math
import torch.nn as nn
import torch
import math
import torch.nn.functional as F

"""SHISRCNet: Super-resolution And Classification Network For Low-resolution Breast Cancer Histopathology Image"""

class oneConv(nn.Module):
    # 卷积+ReLU函数
    def __init__(self, in_channels, out_channels, kernel_sizes, paddings, dilations):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size = kernel_sizes, padding = paddings, dilation = dilations, bias=False),###, bias=False
            # nn.BatchNorm2d(out_channels),
            # nn.ReLU(inplace=True),
        )

    def forward(self, x):
        x = self.conv(x)
        return x

class MSFblock(nn.Module):
    def __init__(self, in_channels):
        super(MSFblock, self).__init__()
        out_channels = in_channels

        self.project = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, 1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(),)
            #nn.Dropout(0.5))
        self.gap = nn.AdaptiveAvgPool2d(1)
        self.softmax = nn.Softmax(dim = 2)
        self.Sigmoid = nn.Sigmoid()
        # 修改为只有两个SE模块，对应两个输入特征
        self.SE1 = oneConv(in_channels,in_channels,1,0,1)
        self.SE2 = oneConv(in_channels,in_channels,1,0,1)

    def forward(self, x1, x2):
        # x1/x2: (B,C,H,W) - 修改为两个输入特征
        y1 = x1
        y2 = x2

        # 通过池化聚合全局信息,然后通过1×1conv建模通道相关性: (B,C,H,W)-->GAP-->(B,C,1,1)-->SE-->(B,C,1,1)
        y1_weight = self.SE1(self.gap(x1))
        y2_weight = self.SE2(self.gap(x2))

        # 将两个特征的全局信息进行拼接: (B,C,2,1)
        weight = torch.cat([y1_weight, y2_weight], 2)
        # 首先通过sigmoid函数获得通道描述符表示, 然后通过softmax函数,求每个特征的权重: (B,C,2,1)--> (B,C,2,1)
        weight = self.softmax(self.Sigmoid(weight))

        # weight[:,:,0]:(B,C,1); (B,C,1)-->unsqueeze-->(B,C,1,1)
        y1_weight = torch.unsqueeze(weight[:,:,0], 2)
        y2_weight = torch.unsqueeze(weight[:,:,1], 2)

        # 将权重与对应的输入进行逐元素乘法: (B,C,1,1) * (B,C,H,W)= (B,C,H,W), 然后将两个特征的输出进行相加
        x_att = y1_weight*y1 + y2_weight*y2
        return self.project(x_att)


if __name__ == '__main__':
    # (B,C,H,W) - 修改为两个输入特征的测试
    x1 = torch.rand(1, 64, 192, 192)
    x2 = torch.rand(1, 64, 192, 192)
    Model = MSFblock(in_channels=64)
    out = Model(x1, x2)
    print(f"Input x1 shape: {x1.shape}")
    print(f"Input x2 shape: {x2.shape}")
    print(f"Output shape: {out.shape}")