import torch
import torch.nn as nn
from timm.models.layers import trunc_normal_
import math


# 特征校正模块
class ChannelWeights(nn.Module):
    def __init__(self, dim, reduction=1):
        super(ChannelWeights, self).__init__()
        self.dim = dim
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        self.mlp = nn.Sequential(
            nn.Linear(self.dim * 6, self.dim * 6 // reduction),
            nn.ReLU(inplace=True),
            nn.Linear(self.dim * 6 // reduction, self.dim * 2),
            nn.Sigmoid())

    def forward(self, x1, x2):
        B, _, H, W = x1.shape
        x = torch.cat((x1, x2), dim=1)
        avg = self.avg_pool(x).view(B, self.dim * 2)    # (B,2C)
        std = torch.std(x, dim=(2, 3), keepdim=True).view(B, self.dim * 2)
        max = self.max_pool(x).view(B, self.dim * 2)
        y = torch.cat((avg, std, max), dim=1)  # B 6C
        y = self.mlp(y).view(B, self.dim * 2, 1)
        channel_weights = y.reshape(B, 2, self.dim, 1, 1).permute(1, 0, 2, 3, 4)  # 2 B C 1 1
        
        return channel_weights


class SpatialWeights(nn.Module):
    def __init__(self, dim, reduction=1):
        super(SpatialWeights, self).__init__()
        self.dim = dim
        self.mlp = nn.Sequential(
            nn.Conv2d(self.dim * 2, self.dim // reduction, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.dim // reduction, 2, kernel_size=1),
            nn.Sigmoid())

    def forward(self, x1, x2):
        B, _, H, W = x1.shape
        x = torch.cat((x1, x2), dim=1)  # B 2C H W
        spatial_weights = self.mlp(x).reshape(B, 2, 1, H, W).permute(1, 0, 2, 3, 4)  # 2 B 1 H W

        return spatial_weights


# 先空间校正再通道校正
class FeatureCorrection_s2c(nn.Module):
    def __init__(self, dim, reduction=1, eps=1e-8):
        super(FeatureCorrection_s2c, self).__init__()
        # 自定义可训练权重参数
        self.weights1 = nn.Parameter(torch.ones(2, dtype=torch.float32), requires_grad=True)
        self.weights2 = nn.Parameter(torch.ones(2, dtype=torch.float32), requires_grad=True)
        self.eps = eps
        self.spatial_weights = SpatialWeights(dim=dim, reduction=reduction)
        self.channel_weights = ChannelWeights(dim=dim, reduction=reduction)

        self.apply(self._init_weights)

    @classmethod
    def _init_weights(cls, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x1, x2):
        # 对可训练权重 self.weights 应用 ReLU 激活函数，确保权重非负
        weights1 = nn.ReLU()(self.weights1)
        # 对经过 ReLU 处理后的权重进行归一化，使得两个权重之和为 1，self.eps 是一个极小值，用于避免分母为零
        fuse_weights1 = weights1 / (torch.sum(weights1, dim=0) + self.eps)
        weights2 = nn.ReLU()(self.weights2)
        fuse_weights2 = weights2 / (torch.sum(weights2, dim=0) + self.eps)

        # 调用 SpatialWeights 模块计算空间权重，该模块接收 x1 和 x2 作为输入，输出形状为 (2, B, 1, H, W) 的空间权重
        spatial_weights = self.spatial_weights(x1, x2)
        # 进行空间校正，将 x2 乘以空间权重的第二部分和融合权重的第一部分，然后加到 x1 上，得到空间校正后的 x1_1
        x1_1 = x1 + fuse_weights1[0] * spatial_weights[1] * x2
        # 同理，对 x2 进行空间校正，得到空间校正后的 x2_1
        x2_1 = x2 + fuse_weights1[1] * spatial_weights[0] * x1

        # 调用 ChannelWeights 模块计算通道权重，该模块接收空间校正后的 x1_1 和 x2_1 作为输入，输出形状为 (2, B, C, 1, 1) 的通道权重
        channel_weights = self.channel_weights(x1_1, x2_1)

        # 进行通道校正，将 x2_1 乘以通道权重的第二部分和融合权重的第二部分，然后加到 x1_1 上，得到最终的主输出 main_out
        main_out = x1_1 + fuse_weights2[0] * channel_weights[1] * x2_1
        # 同理，对 x2_1 进行通道校正，得到最终的辅助输出 aux_out
        aux_out = x2_1 + fuse_weights2[1] * channel_weights[0] * x1_1

        # 返回校正后的两个特征图，形状为(B,C,H,W)
        return main_out, aux_out
