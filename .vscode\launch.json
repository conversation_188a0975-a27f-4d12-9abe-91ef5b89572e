{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {}, "envFile": "${workspaceFolder}/.env", "python": "${workspaceFolder}/venv/Scripts/python.exe"}, {"name": "Python: Train", "type": "python", "request": "launch", "program": "${workspaceFolder}/train.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {}, "envFile": "${workspaceFolder}/.env", "python": "${workspaceFolder}/venv/Scripts/python.exe"}]}