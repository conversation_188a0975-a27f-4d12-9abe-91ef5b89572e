import os
import os.path as osp
import time
import argparse

import torch
import torch.distributed as dist

from .logger import get_logger
from utils.pyt_utils import load_model, parse_devices, extant_file, link_file, ensure_dir

logger = get_logger()

class State(object):
    def __init__(self):
        self.epoch = 1
        self.iteration = 0
        self.dataloader = None
        self.model = None
        self.optimizer = None

    def register(self, **kwargs):
        for k, v in kwargs.items():
            assert k in ['epoch', 'iteration', 'dataloader', 'model',
                         'optimizer']
            setattr(self, k, v)


class Engine(object):
    def __init__(self, custom_parser=None):
        logger.info(
            "PyTorch Version {}".format(torch.__version__))
        self.state = State()
        self.devices = None
        self.distributed = False

        if custom_parser is None:
            self.parser = argparse.ArgumentParser()
        else:
            assert isinstance(custom_parser, argparse.ArgumentParser)
            self.parser = custom_parser

        self.inject_default_parser()
        self.args = self.parser.parse_args()

        self.continue_state_object = self.args.continue_fpath

        # 强制设置为非分布式训练模式，适用于单GPU环境
        self.distributed = False
        try:
            self.devices = parse_devices(self.args.devices)
        except Exception as e:
            logger.warning(f"解析设备参数出错: {e}，默认使用设备0")
            self.devices = [0]  # 默认使用第一个GPU


    def inject_default_parser(self):
        p = self.parser
        p.add_argument('-d', '--devices', default='',
                       help='set data parallel training')
        p.add_argument('-c', '--continue', type=extant_file,
                       metavar="FILE",
                       dest="continue_fpath",
                       help='continue from one certain checkpoint')
        p.add_argument('--local_rank', default=0, type=int,
                       help='process rank on node')
        p.add_argument('-p', '--port', type=str,
                       default='16005',
                       dest="port",
                       help='port for init_process_group')

    def register_state(self, **kwargs):
        self.state.register(**kwargs)

    def update_iteration(self, epoch, iteration):
        self.state.epoch = epoch
        self.state.iteration = iteration

    def save_checkpoint(self, path):
        logger.info("Saving checkpoint to file {}".format(path))
        t_start = time.time()

        # 确保检查点目录存在
        checkpoint_dir = os.path.dirname(path)
        from utils.pyt_utils import ensure_dir
        ensure_dir(checkpoint_dir)
        logger.info("确保检查点目录存在: {}".format(checkpoint_dir))

        state_dict = {}

        from collections import OrderedDict
        new_state_dict = OrderedDict()
        for k, v in self.state.model.state_dict().items():
            key = k
            if k.split('.')[0] == 'module':
                key = k[7:]
            new_state_dict[key] = v
        state_dict['model'] = new_state_dict
        state_dict['optimizer'] = self.state.optimizer.state_dict()
        state_dict['epoch'] = self.state.epoch
        state_dict['iteration'] = self.state.iteration

        t_iobegin = time.time()
        torch.save(state_dict, path)
        del state_dict
        del new_state_dict
        t_end = time.time()
        logger.info(
            "Save checkpoint to file {}, "
            "Time usage:\n\tprepare checkpoint: {}, IO: {}".format(
                path, t_iobegin - t_start, t_end - t_iobegin))

    def link_tb(self, source, target):
        ensure_dir(source)
        ensure_dir(target)
        link_file(source, target)


    def save_and_link_checkpoint(self, checkpoint_dir, log_dir, log_dir_link):
        ensure_dir(checkpoint_dir)
        if not osp.exists(log_dir_link):
            link_file(log_dir, log_dir_link)
        current_epoch_checkpoint = osp.join(checkpoint_dir, 'epoch-{}.pth'.format(
            self.state.epoch))
        self.save_checkpoint(current_epoch_checkpoint)
        last_epoch_checkpoint = osp.join(checkpoint_dir, 'epoch-last.pth')
        link_file(current_epoch_checkpoint, last_epoch_checkpoint)


    def restore_checkpoint(self):
        t_start = time.time()
        try:
            if self.distributed:
                # load the model on cpu first to avoid GPU RAM surge
                # when loading a model checkpoint
                # tmp = torch.load(self.continue_state_object,
                #                  map_location=lambda storage, loc: storage.cuda(
                #                      self.local_rank))
                tmp = torch.load(self.continue_state_object, map_location=torch.device('cpu'))
            else:
                tmp = torch.load(self.continue_state_object)
            t_ioend = time.time()

            # 使用非严格模式加载模型，允许缺少一些键值
            self.state.model = load_model(self.state.model, tmp['model'], is_restore=True, strict=False)

            # 尝试加载优化器状态
            try:
                self.state.optimizer.load_state_dict(tmp['optimizer'])
                logger.info("成功加载优化器状态")
            except Exception as e:
                logger.warning(f"加载优化器状态失败: {e}")
                logger.info("将使用新的优化器状态继续训练")

            # 设置训练状态
            if 'epoch' in tmp:
                self.state.epoch = tmp['epoch'] + 1
                logger.info(f"从第 {self.state.epoch} 轮继续训练")
            else:
                logger.warning("检查点中没有epoch信息，从第1轮开始训练")
                self.state.epoch = 1

            if 'iteration' in tmp:
                self.state.iteration = tmp['iteration']
                logger.info(f"从迭代 {self.state.iteration} 继续训练")
            else:
                logger.warning("检查点中没有iteration信息，从迭代0开始训练")
                self.state.iteration = 0

            del tmp
            t_end = time.time()
            logger.info(
                "Load checkpoint from file {}, "
                "Time usage:\n\tIO: {}, restore checkpoint: {}".format(
                    self.continue_state_object, t_ioend - t_start, t_end - t_ioend))
        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            logger.info("将从头开始训练")
            self.state.epoch = 1
            self.state.iteration = 0


    def __enter__(self):
        return self


    def __exit__(self, type, value, tb):
        torch.cuda.empty_cache()
        if type is not None:
            logger.warning(
                "A exception occurred during Engine initialization, "
                "give up running process")
            return False
