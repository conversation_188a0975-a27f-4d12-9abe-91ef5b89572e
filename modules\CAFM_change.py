import torch
import torch.nn as nn
import torch.nn.functional as F
from einops.einops import rearrange
from modules.OverLoCK_Core import DynamicConvBlock


class GRN(nn.Module):
    """ GRN (Global Response Normalization) layer
    Originally proposed in ConvNeXt V2 (https://arxiv.org/abs/2301.00808)
    This implementation is more efficient than the original (https://github.com/facebookresearch/ConvNeXt-V2)
    We assume the inputs to this layer are (N, C, H, W)
    """
    def __init__(self, channels, use_bias=True):
        super().__init__()
        self.use_bias = use_bias
        self.gamma = nn.Parameter(torch.zeros(1, channels, 1, 1))
        if self.use_bias:
            self.beta = nn.Parameter(torch.zeros(1, channels, 1, 1))

    def forward(self, x):
        Gx = torch.norm(x, p=2, dim=(-1, -2), keepdim=True)
        Nx = Gx / (Gx.mean(dim=1, keepdim=True) + 1e-6)
        if self.use_bias:
            return (self.gamma * Nx + 1) * x + self.beta
        else:
            return (self.gamma * Nx + 1) * x


class CAFM(nn.Module):  # Cross Attention Fusion Module
    def __init__(self, channels, reduction = 1):
        super(CAFM, self).__init__()

        self.conv1_spatial = nn.Conv2d(2, 1, 3, stride=1, padding=1, groups=1)  # group就是标准卷积
        self.conv2_spatial = nn.Conv2d(1, 1, 3, stride=1, padding=1, groups=1)

        self.avg1 = nn.Conv2d(channels, 64, 1, stride=1, padding=0)
        self.avg2 = nn.Conv2d(channels, 64, 1, stride=1, padding=0)
        self.max1 = nn.Conv2d(channels, 64, 1, stride=1, padding=0)
        self.max2 = nn.Conv2d(channels, 64, 1, stride=1, padding=0)

        self.avg11 = nn.Conv2d(64, channels, 1, stride=1, padding=0)
        self.avg22 = nn.Conv2d(64, channels, 1, stride=1, padding=0)
        self.max11 = nn.Conv2d(64, channels, 1, stride=1, padding=0)
        self.max22 = nn.Conv2d(64, channels, 1, stride=1, padding=0)

        self.weights1 = nn.Parameter(torch.ones(2, dtype=torch.float32), requires_grad=True)
        self.weights2 = nn.Parameter(torch.ones(2, dtype=torch.float32), requires_grad=True)
        self.eps = 1e-8

        self.mlp = nn.Sequential(
        nn.Conv2d(channels * 2, channels // reduction, kernel_size=1),
        nn.ReLU(inplace=False),
        nn.Conv2d(channels // reduction, 2, kernel_size=1),
        nn.Sigmoid())

        self.grn1 = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=3, padding=1, groups=channels),
            nn.BatchNorm2d(channels),
            nn.GELU(),
            nn.Conv2d(channels, channels, kernel_size=1),
            GRN(channels),
        )

        self.grn2 = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=3, padding=1, groups=channels),
            nn.BatchNorm2d(channels),
            nn.GELU(),
            nn.Conv2d(channels, channels, kernel_size=1),
            GRN(channels),
        )

        self.overlock1 = DynamicConvBlock(
            dim=channels,
            ctx_dim=channels,
            kernel_size=7,
            smk_size=5,
            num_heads=min(8, channels//4),  # 确保num_heads不会太大
            mlp_ratio=4,
            ls_init_value=1e-5,
            res_scale=True,
            drop_path=0.1,
            use_checkpoint=False
        )

        self.overlock2 = DynamicConvBlock(
            dim=channels,
            ctx_dim=channels,
            kernel_size=7,
            smk_size=5,
            num_heads=min(8, channels//4),  # 确保num_heads不会太大
            mlp_ratio=4,
            ls_init_value=1e-5,
            res_scale=True,
            drop_path=0.1,
            use_checkpoint=False
        )


    def forward(self, f1, f2):
        weights1 = nn.ReLU()(self.weights1)
        fuse_weights1 = weights1 / (torch.sum(weights1, dim=0) + self.eps)

        weights2 = nn.ReLU()(self.weights2)
        fuse_weights2 = weights2 / (torch.sum(weights2, dim=0) + self.eps)

        b, c, h, w = f1.size()  # 获取输入特征的批次大小(b)、通道数(c)、高度(h)、宽度(w)

        f11 = self.overlock1(f1)
        f22 = self.overlock2(f2)

        # 将特征图展平为二维张量 (b, c, h*w)
        f11 = f11.reshape([b, c, -1])
        f22 = f22.reshape([b, c, -1])

        # ---------------------- 第一个特征图的通道注意力 ----------------------
        # 计算全局平均池化和最大池化，这里进行了改变，换成了差分特征
        avg_1 = torch.mean(f11 - f22, dim=-1, keepdim=True).unsqueeze(-1)  # (b, c, 1, 1)
        max_1, _ = torch.max(f11 - f22, dim=-1, keepdim=True)
        max_1 = max_1.unsqueeze(-1)  # (b, c, 1, 1)

        # 通过1x1卷积降维，引入非线性
        avg_1 = F.relu(self.avg1(avg_1), inplace=False)  # (b, 64, 1, 1)
        max_1 = F.relu(self.max1(max_1), inplace=False)  # (b, 64, 1, 1)

        # 恢复原始通道数
        avg_1 = self.avg11(avg_1)  # (b, c, 1, 1)
        max_1 = self.max11(max_1)  # (b, c, 1, 1)

        # 合并平均和最大值特征
        a1 = avg_1 + max_1  # (b, c, 1, 1)
        a1 = torch.sigmoid(a1).reshape([b, c, -1])

        # ---------------------- 第二个特征图的通道注意力 ----------------------
        # 相同操作，处理第二个输入特征
        avg_2 = torch.mean(f22 - f11, dim=-1, keepdim=True).unsqueeze(-1)
        max_2, _ = torch.max(f22 - f11, dim=-1, keepdim=True)
        max_2 = max_2.unsqueeze(-1)

        avg_2 = F.relu(self.avg2(avg_2), inplace=False)
        max_2 = F.relu(self.max2(max_2), inplace=False)
        avg_2 = self.avg22(avg_2)
        max_2 = self.max22(max_2)

        a2 = avg_2 + max_2 # 形状为(B,C,W,H)
        a2 = torch.sigmoid(a2).reshape([b, c, -1])

        # ---------------------- 交叉注意力机制 ----------------------
        # 计算两个特征之间的相关性矩阵 (通道间的注意力)
        cross_channel = torch.matmul(a1, a2.transpose(1, 2))  # (b, c, c)
        # 应用注意力权重到特征上
        a1_weighted = torch.matmul(F.softmax(cross_channel, dim=-1), f11)
        # cross_channel形状为(b, c, c)，f11形状为(b, c, h*w)，输出形状为(b, c, h*w)
        a2_weighted = torch.matmul(F.softmax(cross_channel.transpose(1, 2), dim=-1), f22)  # (b, c, h*w)

        # # 将通道注意力后的特征重新reshape为4D张量
        # a1_4d = a1_weighted.reshape([b, c, h, w])
        # a2_4d = a2_weighted.reshape([b, c, h, w])

        # ---------------------- 空间注意力机制 ----------------------
        f11_fusion = self.grn1(f11.reshape([b, c, h, w]))
        f22_fusion = self.grn2(f22.reshape([b, c, h, w]))
        f = torch.cat((f11_fusion.reshape([b, c, h, w]), f22_fusion.reshape([b, c, h, w])), dim=1) # 形状为(B,2C,H,W)
        spatial_weights = self.mlp(f).reshape(b, 2, 1, h, w).permute(1, 0, 2, 3, 4) # 形状为(2,B,1,H,W)
        b1_weighted = spatial_weights[0].reshape(b, 1, h*w)    # 形状为(B,1,H,W)
        b2_weighted = spatial_weights[1].reshape(b, 1, h*w)
        # # 计算空间维度的统计信息
        # avg_out1 = torch.mean(a1_4d, channels=1, keepchannels=True)  # 通道平均 (b, 1, h, w)
        # max_out1, _ = torch.max(a1_4d, channels=1, keepchannels=True)  # 通道最大 (b, 1, h, w)

        # # 合并统计信息，生成空间注意力图
        # b1 = torch.cat([avg_out1, max_out1], channels=1)  # (b, 2, h, w)
        # b1 = F.relu(self.conv1_spatial(b1))  # (b, 1, h, w)
        # b1 = self.conv2_spatial(b1)  # (b, 1, h, w)
        # b1 = b1.reshape([b, 1, -1])  # (b, 1, h*w)
        # b1 = F.softmax(b1, channels=-1)  # 对空间位置进行softmax

        # # 对第二个特征执行相同的空间注意力操作
        # avg_out2 = torch.mean(a2_4d, channels=1, keepchannels=True)
        # max_out2, _ = torch.max(a2_4d, channels=1, keepchannels=True)
        # b2 = torch.cat([avg_out2, max_out2], channels=1)
        # b2 = F.relu(self.conv1_spatial(b2))
        # b2 = self.conv2_spatial(b2)
        # b2 = b2.reshape([b, 1, -1])
        # b2 = F.softmax(b2, channels=-1)

        # #---------------------- 应用注意力权重 ----------------------
        # # 计算空间交叉注意力
        # cross_spatial = torch.matmul(b1.transpose(1, 2), b2)    # 形状为(b, h*w, h*w)

        # # 应用空间注意力权重 - 修正维度匹配
        # spatial_attn1 = F.softmax(cross_spatial, channels=-1)  # (b, h*w, h*w)
        # spatial_attn2 = F.softmax(cross_spatial.transpose(1, 2), channels=-1)  # (b, h*w, h*w)

        # # 应用空间注意力到特征上
        # b1_weighted = torch.matmul(spatial_attn1, f11.transpose(1, 2)) # (b, h*w, c)
        # b2_weighted = torch.matmul(spatial_attn2, f22.transpose(1, 2)) # (b, h*w, c)

        # 最终融合
        f1_out = f1.reshape(b, c, h*w) + fuse_weights1[0] * a1_weighted * f11 + fuse_weights1[1] * b2_weighted * f11
        f2_out = f2.reshape(b, c, h*w) + fuse_weights2[0] * a2_weighted * f22 + fuse_weights2[1] * b1_weighted * f22

        return f1_out.reshape([b, c, h, w]), f2_out.reshape([b, c, h, w])

# cafm = CAFM(channels= 32)
# x1 = torch.randn(10, 32, 16, 16)
# x2 = torch.randn(10, 32, 16, 16)
# out = cafm(x1, x2)
# print(out[0].shape)