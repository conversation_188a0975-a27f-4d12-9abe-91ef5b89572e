import os.path as osp
import os

if os.name == 'nt':  # Windows系统
    remoteip = os.popen('cd').read()
else:
    remoteip = os.popen('pwd').read()

class config:
    # 基础配置
    root_dir = os.path.abspath(os.path.join(os.getcwd(), './'))

    # 随机种子配置
    seed = 12345

    # Dataset config - PIE-RGB-SAR数据集配置
    dataset_name = 'PIE-RGB-SAR'
    dataset_path = osp.join(root_dir, 'datasets', 'PIE-RGB-SAR')
    rgb_root_folder = osp.join(dataset_path, 'RGB')
    rgb_format = '.tif'
    gt_root_folder = osp.join(dataset_path, 'Label')
    gt_format = '.tif'
    gt_transform = True
    # True when label 0 is invalid, you can also modify the function _transform_gt in dataloader.RGBXDataset
    # True for most dataset valid, False for MFNet(?)
    x_root_folder = osp.join(dataset_path, 'SAR')
    x_format = '.tif'
    x_is_single_channel = True  # 设置为True，因为SAR图像是单通道的
    train_source = osp.join(dataset_path, "train.txt")
    eval_source = osp.join(dataset_path, "val.txt")
    is_test = False
    num_train_imgs = 2432  # 数据集重新划分，训练集：验证集 = 1：1
    num_eval_imgs = 2433   # 数据集重新划分，训练集：验证集 = 1：1
    num_classes = 6        # 根据标签分析，有6个类别（0-5）
    class_names = ['background', 'building', 'farmland', 'forest', 'water', 'road']  # 根据实际类别含义修改

    # 图像配置
    background = 255  # 设置为255，与RGBXDataset._gt_transform中的处理一致
    image_height = 256  # 调整为更合适的尺寸
    image_width = 256
    norm_mean = [0.348, 0.370, 0.309]  # 根据PIE-RGB-SAR数据集统计得到
    norm_std = [0.197, 0.174, 0.171]   # 根据PIE-RGB-SAR数据集统计得到

    # 模型配置
    backbone = 'swin_s'
    decoder = 'UPerHead'  # 解码器类型
    pretrained_model = osp.join(root_dir, 'swintrans', 'swin_small_patch4_window7_224_22k.pth')
    
    # Triple Backbone Configuration - ConvNeXt-V2
    use_triple_backbone = True  # 启用三主干架构
    convnextv2_backbone_config = {
        'in_chans': 3,
        'num_classes': 0,  # 设置为0，因为我们只需要特征提取
        'depths': [3, 3, 27, 3],  # ConvNeXt-V2 Base配置
        'dims': [128, 256, 512, 1024],  # ConvNeXt-V2 Base维度
        'drop_path_rate': 0.2,
        'head_init_scale': 1.0,
        'out_indices': (0, 1, 2, 3),  # 输出四个阶段的特征
        'pretrained_path': osp.join(root_dir, 'convnextv2', 'convnextv2_base_22k_384_ema.pt')
    }
    
    # 训练配置
    lr = 1e-5  # 降低学习率，避免梯度爆炸
    lr_power = 0.9
    momentum = 0.9
    weight_decay = 0.01
    batch_size = 8  # 减小批处理大小以适应单张显卡
    nepochs = 300
    niters_per_epoch = num_train_imgs // batch_size + 1
    num_workers = 0  # Windows系统下设置为0，避免多进程问题
    train_scale_array = [0.5, 0.75, 1, 1.25, 1.5, 1.75]
    warm_up_epoch = 20  # 增加预热轮数，使训练更加稳定

    # 网络配置
    fix_bias = True
    bn_eps = 1e-3
    bn_momentum = 0.1

    # 优化器配置
    optimizer = 'AdamW'  # 优化器类型：'AdamW' 或 'SGDM'
    grad_clip = 1.0  # 梯度裁剪阈值
    
    # 损失函数配置
    loss_type = 'simplified_combined'  # 损失函数类型：'ce', 'combined', 'adaptive', 'simplified_combined', 'multimodal_combined'

    # 损失函数权重配置
    loss_weights = {
        'ce_weight': 1.0,
        'focal_weight': 0.0,
        'dice_weight': 0.0,
        'lovasz_weight': 0.0,
        'boundary_weight': 0.0,
        'context_weight': 0.0
    }

    # Focal Loss参数
    focal_gamma = 2.0
    focal_alpha = None  # 可以设置为类别权重列表或None

    # 标签平滑
    label_smoothing = 0.0

    # 类别权重（可选）
    class_weights = None  # 例如：[1.0, 2.0, 1.5, 1.0, 1.0, 1.0] 对应6个类别

    # 简化组合损失函数的上下文参数
    context_kernel_size = 3
    context_dilation = 1

    # 自适应损失函数参数
    adaptive_initial_weights = {'ce_weight': 1.0, 'focal_weight': 0.5, 'dice_weight': 0.3, 'lovasz_weight': 0.2}
    adaptive_min_weight = 0.1
    adaptive_max_weight = 2.0
    adaptive_weight_decay = 0.01
    adaptive_temperature = 1.0
    adaptive_weight_lr_ratio = 0.1
    adaptive_freeze_after_epochs = None

    # 日志配置
    log_loss_components = True  # 是否记录损失组件详细信息
    
    # 评估配置
    eval_iter = 25
    eval_stride_rate = 2 / 3
    eval_scale_array = [1]  # 使用单一尺度进行评估
    eval_flip = False  # 不使用翻转增强
    eval_crop_size = [256, 256]  # 调整为与训练图像尺寸一致
    eval_batch_size = 12  # 验证时的batch size，比训练时更大
    eval_frequency = 5  # 每5轮进行一次验证

    # 存储配置
    checkpoint_start_epoch = 1  # 从第一轮开始保存检查点
    checkpoint_step = 5  # 每5轮保存一次检查点

    # 恢复配置
    resume_from = None  # 从指定检查点继续训练
    resume_model = None  # 兼容旧版本代码

    # 其他配置
    log_loss_components = True  # 是否记录损失组件详细信息

    # 日志配置
    log_dir = osp.abspath('log_' + dataset_name + '_' + backbone + '_convnextv2')
    tb_dir = osp.abspath(osp.join(log_dir, "tb"))
    checkpoint_dir = osp.abspath(osp.join(log_dir, "checkpoint"))
    
    import time
    exp_time = time.strftime('%Y_%m_%d_%H_%M_%S', time.localtime())
    log_file = log_dir + '/log_' + exp_time + '.log'
    val_log_file = log_dir + '/val_' + exp_time + '.log'

# 创建全局配置实例
config = config()
